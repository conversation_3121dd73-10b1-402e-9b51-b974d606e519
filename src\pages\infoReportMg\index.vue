<route lang="jsonc">
{
  "layout": "tabbar",
  "style": {
    "navigationBarTitleText": "商品通报管理",
    "enablePullDownRefresh": true,
    "backgroundColor": "#f0f3f8"
  }
}
</route>

<script lang="ts" setup>
import type { ReportOverviewRes } from '@/service/infoReportApi'
import { DesignImage } from '@/components/image'
import { reportOverviewApi } from '@/service/infoReportApi'

import { useUserStore } from '@/store/user'

const overviewData = ref<ReportOverviewRes['data']>()

function toReportPage(url: string) {
  uni.navigateTo({
    url,
  })
}

function getOverview() {
  return new Promise((resolve, reject) => {
    reportOverviewApi(useUserStore().userId).then((res) => {
      overviewData.value = res.data
      resolve(true)
    }).catch((err) => {
      reject(err)
    })
  })
}

const canUseAgencyNum = computed(() => {
  if (!overviewData.value) {
    return 0
  }

  const agencyTotal = overviewData.value.agencyTotal ?? 0
  const completedNum = overviewData.value.completedNum ?? 0

  const result = agencyTotal - completedNum
  return result < 0 ? 0 : result
})

function toCreateQuickReport() {
  uni.navigateTo({ url: '/pages/infoReportMg/infoReportDetailFromPage?action=create' })
}
function toInfoReport() {
  uni.navigateTo({ url: '/pages/infoReport/myInfoReport' })
}

onPullDownRefresh(() => {
  getOverview().finally(() => {
    uni.stopPullDownRefresh()
  })
})

onLoad(() => {
  getOverview()
})
</script>

<template>
  <view class="p-3">
    <view class="flex space-x-2">
      <view
        class="o-btn-light-bg-blue-border o-shadow-blue-light center flex-1 flex-col py-3"
        @click="toCreateQuickReport"
      >
        <image :src="DesignImage.btnIcon.cameras" mode="widthFix" class="f-btn-icon-lg" />
        <text class="f-btn-text">
          逐个产品通报
        </text>
      </view>
      <view
        class="o-btn-light-bg-blue-border o-shadow-blue-light center flex-1 flex-col py-3"
        @click="toInfoReport"
      >
        <image :src="DesignImage.btnIcon.file" mode="widthFix" class="f-btn-icon-lg" />
        <text class="f-btn-text">
          整批产品通报
        </text>
      </view>
    </view>
    <!-- <view class="mt-2 rounded-lg bg-white p-4">
      <view class="text-lg font-bold">
        条码容量占用情况：
      </view>
      <view class="mt-1 flex items-center justify-end text-gray">
        <view>已使用：21</view>
        <view>
          <text class="px-1">
            /
          </text>总量：10000
        </view>
      </view>
      <up-line-progress class="mt-2" :percentage="30" :active-color="Color.primary" />
    </view> -->
    <view class="mt-2 rounded-lg bg-white p-4">
      <view class="text-lg font-bold">
        逐个通报情况：
      </view>
      <view class="grid grid-cols-3 mt-1">
        <view class="flex flex-col items-center text-primary">
          <view>可用个数</view>
          <view class="text-lg font-bold">
            {{ overviewData?.unReportNum }}
          </view>
        </view>
        <view class="flex flex-col items-center border-l border-r border-l-gray-200 border-r-gray-200 border-l-solid border-r-solid text-emerald-500">
          <view>已用个数</view>
          <view class="text-lg font-bold">
            {{ overviewData?.reportNum }}
          </view>
        </view>
        <view class="flex flex-col items-center">
          <view>通报总个数</view>
          <view class="text-lg font-bold">
            {{ overviewData?.totalNum }}
          </view>
        </view>
      </view>
      <view
        class="o-bg-primary o-shadow-blue o-shadow-blue mt-4 rounded py-2 text-center text-white"
        @click="toReportPage('/pages/infoReportMg/infoReportListPage')"
      >
        逐个产品管理
      </view>
    </view>
    <view class="mt-2 rounded-lg bg-white p-4">
      <view class="text-lg font-bold">
        整批通报情况：
      </view>
      <view class="grid grid-cols-3 mt-1">
        <view class="flex flex-col items-center text-primary">
          <view>可用批次</view>
          <view class="text-lg font-bold">
            {{ canUseAgencyNum }}
          </view>
        </view>
        <view class="flex flex-col items-center border-l border-r border-l-gray-200 border-r-gray-200 border-l-solid border-r-solid text-emerald-500">
          <view>已完成批次</view>
          <view class="text-lg font-bold">
            {{ overviewData?.completedNum }}
          </view>
        </view>
        <view class="flex flex-col items-center">
          <view>总批次</view>
          <view class="text-lg font-bold">
            {{ overviewData?.agencyTotal }}
          </view>
        </view>
      </view>
      <view
        class="o-bg-primary o-shadow-blue o-shadow-blue mt-4 rounded py-2 text-center text-white"
        @click="toReportPage('/pages/infoReport/myInfoReport')"
      >
        整批通报管理
      </view>
    </view>
    <!-- <view class="mt-3 rounded-lg bg-white p-4">
      <view class="mb-2 text-xl font-bold">
        产品风险检测情况
      </view>
      <view class="w-full text-right">
        <text class="text-primary">
          检测中：21
        </text>
        <text class="text-gray">
          <text class="px-1">
            /
          </text><text>
            检查总数：10000
          </text>
        </text>
      </view>
      <view class="mt-3 flex items-center justify-between">
        <view>
          <text class="text-gray">
            未通报
          </text>
          <text class="text-red">
            1
          </text>
        </view>
        <view class="flex items-center space-x-1">
          <view class="text-primary">
            去解决
          </view>
          <up-icon name="arrow-right" :color="Color.primary" size="12" class="shrink-0" />
        </view>
      </view>
      <view class="o-bg-primary o-shadow-blue o-shadow-blue mt-4 rounded py-2 text-center text-white">
        产品风险检测管理
      </view>
    </view> -->
    <view class="o-pb" />
  </view>
</template>

<style lang="scss" scoped>
.f-btn-md {
  @apply py-2;
  width: 272rpx;
}

.f-btn-icon-lg {
  width: 213rpx;
}

.f-btn-icon-md {
  $w: 146rpx;
  width: $w;
}

.f-btn-icon-sm {
  width: 120rpx;
}
</style>
