<script lang="ts" setup>
interface Props {
  title: string
  content?: any
  contentClass?: string
}

const props = withDefaults(defineProps<Props>(), {
  content: '',
  contentClass: '',
})

const slots = useSlots()

// 判断是否有值的计算属性
const hasValue = computed(() => {
  // 如果有插槽内容，则显示
  if (slots.default) {
    return true
  }
  // 否则判断 content 是否有值
  return props.content != null && props.content !== undefined && props.content !== ''
})
</script>

<template>
  <view v-if="hasValue" class="mt-2 flex text-sm leading-4">
    <view class="shrink-0 text-right text-gray" style="min-width: 180rpx;">
      {{ title }}：
    </view>
    <view :class="contentClass">
      <slot>
        {{ content }}
      </slot>
    </view>
  </view>
</template>

<style lang="scss" scoped></style>
