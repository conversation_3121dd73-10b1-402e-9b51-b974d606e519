import type { CurrencyType, GoodsType, InfoReportBarType, SyncAddState, SyncWxState } from '@/enums'
import type { OrderDirection } from '@/enums/httpEnum'
import { http } from '@/http/http'

// 参数接口
export interface ReportGoodsLoadV2Params {
  goodsId?: number
  userId?: number
  barCode?: string
}

export interface ReportGoodsLoadV2Res {
  data: {
    attribueList: {
      attributeId: string
      attributeName: string
      attributeValue: string
    }[]
    barCode: string
    barType: InfoReportBarType
    brandName: string
    commonName: string
    companyPrice: number
    currency: string | null
    goodsDescription: string
    goodsId: number
    goodsName: string
    goodsType: GoodsType
    gpcType: string
    gpcTypeName: string
    imageList: {
      imageDecs: string
      imageUrl: string
      isMain: boolean
    }[]
    isPrivary: boolean
    marketDate: string
    netContent: string
    netContentUnit: string
    packageCode: number
    productFeatures: string
    spec: string
    standardList: {
      executeStandard: string
      executeYear: string
      standardNumber: string
    }[]
    syncAddDate: string
    syncAddMsg: string
    syncAddState: SyncAddState
    syncAddStateName: string
    syncDate: string
    syncMsg: string
    syncState: SyncWxState
    syncStateName: string
  }
  errCode: string
  errMessage: string
  success: boolean
}
/**
 * 加载通报商品V2
 * @returns
 * @param params
 */
export function reportGoodsLoadV2Api(params: ReportGoodsLoadV2Params) {
  return http.post<ReportGoodsLoadV2Res>('/api/reportGoodsLoadV2', {}, params, false, {
    header: {
      'Content-Type': 'application/x-www-form-urlencoded',
    },
  })
}

// 参数接口
export interface ReportGoodsPageParams {
  barCode?: string
  brandName?: string
  certificationId?: number
  endDate?: string
  goodsName?: string
  goodsNameAndCode?: string
  groupBy?: string
  needTotalCount?: boolean
  orderBy?: string
  orderDirection?: string
  pageIndex?: number
  pageSize?: number
  startDate?: string
  syncAddStateList?: number[]
  userId: number
}

// 响应接口
export interface ReportGoodsPageRes {
  data: {
    barCode: string
    barType: InfoReportBarType
    brandName: string
    certificationId: number
    commonName: string
    companyPrice: number
    currency: string
    goodsDescription: string
    goodsId: number
    goodsName: string
    goodsType: GoodsType
    gpcType: string
    gpcTypeName: string
    isAccountError: boolean
    isPrivary: boolean
    lastUpdatedDate: string
    marketDate: string
    netContent: string
    productFeatures: string
    spec: string
    syncAddDate: string
    syncAddMsg: string
    syncAddState: SyncAddState
    syncAddStateName: string
    syncDate: string
    syncMsg: string
    syncState: SyncWxState
    syncStateName: string
  }[]
  empty: boolean
  errCode: string
  errMessage: string
  notEmpty: boolean
  pageIndex: number
  pageSize: number
  success: boolean
  totalCount: number
  totalPages: number
}
/**
 * 通报商品分页
 * @param {object} params qry
 * @param {string} params.barCode 条码信息
 * @param {string} params.brandName 品牌名称
 * @param {number} params.certificationId 认证id/企业id，因为前期不需要认证，所以不需要填写
 * @param {string} params.endDate 修改结束时间
 * @param {string} params.goodsName 产品名称
 * @param {string} params.groupBy
 * @param {boolean} params.needTotalCount
 * @param {string} params.orderBy
 * @param {string} params.orderDirection
 * @param {number} params.pageIndex
 * @param {number} params.pageSize
 * @param {string} params.startDate 修改开始时间
 * @param {number} params.userId 用户id
 * @returns
 */
export function reportGoodsPageApi(params: ReportGoodsPageParams) {
  return http.post<ReportGoodsPageRes>('/api/reportGoodsPage', params)
}

// 通报商品公共参数接口
export interface ReportGoodsBaseParams {
  attribueList?: {
    attributeId: string
    attributeName?: string
    attributeValue?: string
  }[]
  barCode: string
  barType: InfoReportBarType
  brandName: string
  commonName: string
  companyPrice?: number
  currency?: CurrencyType
  goodsDescription: string
  goodsName: string
  goodsType: GoodsType
  gpcType: string
  imageList: {
    imageDecs?: string
    imageUrl?: string
    isMain?: boolean
  }[]
  isPrivary: boolean
  marketDate?: string
  netContent: string
  netContentUnit: string
  productFeatures?: string
  spec: string
  standardList?: {
    executeStandard?: string
    executeYear?: string
    standardNumber?: string
  }[]
}

// 新增参数接口
export interface ReportGoodsAddParams extends ReportGoodsBaseParams {
  userId: number
}

// 响应接口
export interface ReportGoodsAddRes {
  errCode: string
  errMessage: string
  success: boolean
}
/**
 * 新增通报商品
 * @param {object} params cmd
 * @param {Array} params.attribueList 产品属性列表,根据分类不同，属性不同
 * @param {string} params.barCode 条码
 * @param {string} params.barType 条码类型:UPC-A/EAN/ITF
 * @param {string} params.brandName 品牌名称
 * @param {string} params.commonName 产品通用名
 * @param {number} params.companyPrice 企业定价,如果填写，要填写大于0的数字，如果币种选了，价格一定要填写
 * @param {string} params.currency 币种,如：人民币
 * @param {string} params.goodsDescription 产品描述
 * @param {string} params.goodsName 产品名称
 * @param {string} params.goodsType 产品状态填写“在产或不在产”
 * @param {string} params.gpcType 产品（GPC）分类
 * @param {Array} params.imageList 产品图片列表
 * @param {boolean} params.isPrivary 是否保密
 * @param {string} params.marketDate （预计）上市时间
 * @param {string} params.netContent 净含量
 * @param {string} params.netContentUnit 净含量单位
 * @param {string} params.productFeatures 产品特征
 * @param {string} params.spec 规格
 * @param {Array} params.standardList 产品标准列表
 * @param {number} params.userId 用户id
 * @returns
 */
export function reportGoodsAddApi(params: ReportGoodsAddParams) {
  return http.post<ReportGoodsAddRes>('/api/reportGoodsAdd', params)
}

// 编辑参数接口
export interface ReportGoodsUpdateParams extends ReportGoodsBaseParams {
  goodsId: number
}

// 响应接口
export interface ReportGoodsUpdateRes {
  errCode: string
  errMessage: string
  success: boolean
}
/**
 * 编辑通报商品
 * @param {object} params cmd
 * @param {Array} params.attribueList 产品属性列表,根据分类不同，属性不同
 * @param {string} params.barCode 条码
 * @param {string} params.barType 条码类型:UPC-A/EAN/ITF
 * @param {string} params.brandName 品牌名称
 * @param {string} params.commonName 产品通用名
 * @param {number} params.companyPrice 企业定价
 * @param {string} params.currency 币种,如：人民币
 * @param {string} params.goodsDescription 产品描述
 * @param {number} params.goodsId 商品id
 * @param {string} params.goodsName 产品名称
 * @param {string} params.goodsType 产品状态填写“在产或不在产”
 * @param {string} params.gpcType 产品（GPC）分类
 * @param {Array} params.imageList 产品图片列表
 * @param {boolean} params.isPrivary 是否保密
 * @param {string} params.marketDate （预计）上市时间
 * @param {string} params.netContent 净含量
 * @param {string} params.netContentUnit 净含量单位
 * @param {string} params.productFeatures 产品特征
 * @param {string} params.spec 规格
 * @param {Array} params.standardList 产品标准列表
 * @returns
 */
export function reportGoodsUpdateApi(params: ReportGoodsUpdateParams) {
  return http.post<ReportGoodsUpdateRes>('/api/reportGoodsUpdate', params)
}

export interface ReportGpcPageParams {
  gpcTypeName?: string
  groupBy?: string
  level?: number
  needTotalCount?: boolean
  orderBy?: string
  orderDirection?: OrderDirection
  pageIndex?: number
  pageSize?: number
  pid?: number
}

export interface ReportGpcPageRes {
  data: {
    descriptionEn: string
    gpcType: string
    gpcTypeName: string
    id: number
    level: number
    pid: number
  }[]
  empty: boolean
  errCode: string
  errMessage: string
  notEmpty: boolean
  pageIndex: number
  pageSize: number
  success: boolean
  totalCount: number
  totalPages: number
}
/**
 * 分页查询GPC分类
 * @param {object} params qry
 * @param {string} params.gpcTypeName 描述
 * @param {string} params.groupBy
 * @param {number} params.level 层级，第一层填1，第二层：2，第三次：3，第4层：4
 * @param {boolean} params.needTotalCount
 * @param {string} params.orderBy
 * @param {string} params.orderDirection
 * @param {number} params.pageIndex
 * @param {number} params.pageSize
 * @param {number} params.pid 父类id,层级为1时不需要填写，其他需要填写
 * @returns
 */
export function reportGpcPageApi(params: ReportGpcPageParams) {
  return http.post<ReportGpcPageRes>('/api/gpcPage', params)
}

export interface GpcNationalChildrenData {
  gpcCode: string
  name: string
  children: GpcNationalChildrenData[]
}

export interface GpcNationalListRes {
  data: GpcNationalChildrenData[]
  empty: boolean
  errCode: string
  errMessage: string
  notEmpty: boolean
  success: boolean
}
/**
 * 国补GPC
 * @returns
 */
export function gpcNationalListApi() {
  return http.post<GpcNationalListRes>('/api/gpcNationalList')
}

export interface GpcAttributeListParams {
  code: string
  groupBy?: string
  needTotalCount?: boolean
  orderBy?: string
  orderDirection?: OrderDirection
  pageIndex?: number
  pageSize?: number
}

export interface AttributeListItem {
  attributeCntitle: string
  attributeCode: string
  attributeId: string
  attributeValueList: {
    attributeValue: string
  }[]
  isRequired: boolean
}

export interface GpcAttributeListRes {
  data: {
    attributeList: AttributeListItem[]
    code: string
  }
  errCode: string
  errMessage: string
  success: boolean
}
/**
 * 查询GPC分类的属性列表
 * @param {object} params qry
 * @param {string} params.code 分类编号
 * @param {string} params.groupBy
 * @param {boolean} params.needTotalCount
 * @param {string} params.orderBy
 * @param {string} params.orderDirection
 * @param {number} params.pageIndex
 * @param {number} params.pageSize
 * @returns
 */
export function gpcAttributeListApi(params: GpcAttributeListParams) {
  return http.post<GpcAttributeListRes>('/api/gpcAttributeList', params, {}, true)
}

// 参数接口
export interface GpcPageParams {
  gpcTypeName?: string
  groupBy?: string
  level?: number
  needTotalCount?: boolean
  orderBy?: string
  orderDirection?: string
  pageIndex?: number
  pageSize?: number
  pid?: number
}

// 参数接口
export interface BrandCreateParams {
  brandCreatedDate?: string
  brandEnName?: string
  brandName: string
  description?: string
  logoUrl?: string
  userId: number
}

// 响应接口
export interface BrandCreateRes {
  errCode: string
  errMessage: string
  success: boolean
}
/**
 * 新增品牌
 * @param {object} params cmd
 * @param {string} params.brandName 品牌名称
 * @param {number} params.userId 用户id
 * @returns
 */
export function brandCreateApi(params: BrandCreateParams) {
  return http.post<BrandCreateRes>('/api/brandCreate', params)
}

// 参数接口
export interface BrandListParams {
  brandName?: string
  userId: number
}

// 响应接口
export interface BrandListRes {
  data: {
    brandId: number
    brandName: string
    userId: number
  }[]
  empty: boolean
  errCode: string
  errMessage: string
  notEmpty: boolean
  success: boolean
}
/**
 *  品牌列表
 * @param {object} params qry
 * @param {string} params.brandName 品牌名称
 * @param {number} params.userId 用户id
 * @returns
 */
export function brandListApi(params: BrandListParams) {
  return http.post<BrandListRes>('/api/brandList', params)
}

// 响应接口
export interface BrandLoadRes {
  data: {
    brandCreatedDate: string
    brandEnName: string
    brandId: number
    brandName: string
    description: string
    logoUrl: string
    userId: number
  }
  errCode: string
  errMessage: string
  success: boolean
}

/**
 * 加载品牌
 * @param {string} brandId 品牌ID
 * @returns
 */
export function brandLoadApi(brandId: number) {
  return http.post<BrandLoadRes>('/api/brandLoad', {}, { brandId }, false, {
    header: {
      'Content-Type': 'application/x-www-form-urlencoded',
    },
  })
}

// 响应接口
export interface Gs1NetContentListRes {
  data: {
    codeUomnCname: string
  }[]
  empty: boolean
  errCode: string
  errMessage: string
  notEmpty: boolean
  success: boolean
}
/**
 * 加载净含量单位
 * @returns
 */
export function gs1NetContentListApi() {
  return http.post<Gs1NetContentListRes>('/api/gs1NetcontentList')
}

// 响应接口
export interface ReportOverviewRes {
  data: {
    agencyTotal: number
    completedNum: number
    failNum: number
    reportNum: number
    reportingNum: number
    successNum: number
    totalGoodsNum: number
    totalNum: number
    unReportNum: number
    userId: number
  }
  errCode: string
  errMessage: string
  success: boolean
}
/**
 * 通报概况
 * @param {string} userId 用户id
 * @returns
 */
export function reportOverviewApi(userId: number) {
  return http.post<ReportOverviewRes>('/api/reportOverview', {}, { userId }, false, {
    header: {
      'Content-Type': 'application/x-www-form-urlencoded',
    },
  })
}

// 参数接口
export interface AiImageUrlInfoParams {
  imageUrl: string
  isFirst: boolean
}

// 响应接口
export interface AiImageUrlInfoRes {
  data: {
    bottleNetContent: string
    bottleNum: string
    boxNum: string
    brandName: string
    commonName: string
    companyPrice: string
    currency: string
    executeStandard: string
    executeYear: string
    goodsDescription: string
    goodsName: string
    goodsType: string
    gpcType: string
    marketDate: string
    netContent: string
    netContentUnit: string
    odorType: string
    productFeatures: string
    salesUnit: string
    spec: string
    standardNumber: string
    vol: string
    wineType: string
  }
  errCode: string
  errMessage: string
  success: boolean
}
/**
 * 上传图片到OSS再回识别信息——保存原图片
 * @param {object} params qry
 * @param {string} params.imageUrl 图片url
 * @param {boolean} params.isFirst 是否第一次访问，true:是，false:否
 * @returns
 */
export function aiImageUrlInfoApi(params: AiImageUrlInfoParams) {
  return http.post<AiImageUrlInfoRes>('/api/aiImageUrlInfo', params)
}
