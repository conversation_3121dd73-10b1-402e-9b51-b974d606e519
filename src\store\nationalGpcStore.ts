import { defineStore } from 'pinia'

export interface GpcNationalChildrenDataWithLevel {
  gpcCode: string
  name: string
  level: number
  children: GpcNationalChildrenDataWithLevel[]
}
export const nationalGpcStore = defineStore('nationalGpcStore', () => {
  const nationalGpcData = ref<GpcNationalChildrenDataWithLevel[]>([])
  const levelNationalGpcData = ref<GpcNationalChildrenDataWithLevel[]>([])
  function getLevelNationalGpcData() {
    return levelNationalGpcData.value
  }

  return { nationalGpcData, levelNationalGpcData, getLevelNationalGpcData }
})
