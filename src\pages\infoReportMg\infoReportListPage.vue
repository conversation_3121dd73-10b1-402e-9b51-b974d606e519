<route lang="jsonc">
{
  "style": {
    "navigationBarTitleText": "商品通报管理"
  }
}
</route>

<script lang="ts" setup>
import type { ReportGoodsPageRes, ReportOverviewRes } from '@/service/infoReportApi'
import type { CheckGroupChangeType } from '@/types/myType'
import debounce from 'debounce'
import { storeToRefs } from 'pinia'
import { DesignImage } from '@/components/image'
import { Color } from '@/enums/colorEnum'
import { OrderDirection } from '@/enums/httpEnum'
import { getSyncAddStateStr, getSyncWxStateStr, SyncAddState, SyncWxState } from '@/enums/index'
import { reportGoodsPageApi, reportOverviewApi } from '@/service/infoReportApi'
import { updateUserPhoneApi } from '@/service/systemApi'
import { useUserStore } from '@/store/user'

const userStore = useUserStore()
const { userId, barCodeCardNum, barCodeCardPassword, phone } = storeToRefs(userStore)

const PAGE_SIZE = 10

const keyword = ref('')
const isShowFilter = ref(false)
const isShowKeyModal = ref(false)
const isFilter = ref(false)
const isShowInfoBar = ref(false)
const productList = ref<ReportGoodsPageRes['data']>([])
const refreshTriggered = ref(false)
const loading = ref(false)
const overviewLoading = ref(false)
const hasMore = ref(true)
const pageIndex = ref(1)
const overviewData = ref<ReportOverviewRes['data']>()
const syncAddStateArr = ref<SyncAddState[]>([SyncAddState.success, SyncAddState.processing, SyncAddState.failed, SyncAddState.new])
const isShowIncorrectPasswordTip = ref(false)
const scrollTop = ref(0)

enum Status {
  fail = 0,
  done = 1,
  processing = 2,
}

function getReportGoodsList() {
  loading.value = true
  reportGoodsPageApi({
    goodsNameAndCode: keyword.value,
    needTotalCount: true,
    orderBy: 'goodsId',
    orderDirection: OrderDirection.desc,
    pageIndex: pageIndex.value,
    pageSize: PAGE_SIZE,
    // startDate:
    syncAddStateList: syncAddStateArr.value,
    userId: useUserStore().userId,
  }).then((res) => {
    if (pageIndex.value === 1) {
      productList.value = res.data
    }
    else {
      productList.value = [...productList.value, ...res.data]
    }

    // 判断是否还有更多数据
    hasMore.value = res.data.length === PAGE_SIZE

    // 只有在成功获取数据后才递增页码
    pageIndex.value++
  }).finally(() => {
    loading.value = false
    refreshTriggered.value = false
  })
}

const handleInput = debounce(() => {
  pageIndex.value = 1
  hasMore.value = true
  getReportGoodsList()
}, 800)

function getOverview() {
  overviewLoading.value = true
  reportOverviewApi(useUserStore().userId).then((res) => {
    overviewData.value = res.data
  }).finally(() => {
    overviewLoading.value = false
  })
}

function getStatusStrAndColor(data: ReportGoodsPageRes['data'][0]) {
  const addStr = getSyncAddStateStr(data.syncAddState)
  const wxStr = getSyncWxStateStr(data.syncState)
  switch (data.syncAddState) {
    case SyncAddState.failed:
      return [addStr, 'text-red-500']
    case SyncAddState.success:
      // 再判断微信共享情况
      switch (data.syncState) {
        case SyncWxState.failed:
          return [wxStr, 'text-red-500']
        case SyncWxState.success:
          return [addStr, 'text-emerald-500']
        case SyncWxState.processing:
          return [wxStr, 'text-primary']
        default:
          return [wxStr, 'text-gray-500']
      }
    case SyncAddState.processing:
      return [addStr, 'text-primary']
    default:
      return [addStr, 'text-gray-500']
  }
}
function onRefresh() {
  if (refreshTriggered.value)
    return
  pageIndex.value = 1
  hasMore.value = true
  refreshTriggered.value = true
  getReportGoodsList()
  getOverview()
}

function onScrollToLower() {
  if (loading.value || !hasMore.value) {
    return
  }
  getReportGoodsList()
}

function showDetail(goodsId: number) {
  uni.navigateTo({
    url: `/pages/infoReportMg/infoReportDetailPage?goodsId=${goodsId}`,
  })
}

function checkboxChange(e?: CheckGroupChangeType) {
  if (e?.detail?.value) {
    // syncAddStateArr.value = e.detail.value
    syncAddStateArr.value = e?.detail?.value.map((item) => {
      return Number(item)
    })
    pageIndex.value = 1
    hasMore.value = true
    getReportGoodsList()
  }
}

function savePasswordAndReport() {
  if (barCodeCardNum.value === '' || barCodeCardPassword.value === '') {
    uni.showToast({
      title: '请输入条码卡号和密码',
      icon: 'none',
      duration: 2000,
    })
    return
  }
  updateUserPhoneApi({
    barCodeCardNum: barCodeCardNum.value,
    barCodeCardPassword: barCodeCardPassword.value,
    phone: phone.value,
    userId: userId.value,
  }).then(() => {
    userStore.setUpdatePasswordTime()
    isShowIncorrectPasswordTip.value = false
  }).finally(() => {
    isShowKeyModal.value = false
  })
}

function toCreateQuickReport() {
  uni.navigateTo({ url: '/pages/infoReportMg/infoReportDetailFromPage?action=create' })
}
function toInfoReport() {
  uni.navigateTo({ url: '/pages/infoReport/myInfoReport' })
}

function scrollToTop() {
  // 通过改变值来触发滚动到顶部
  scrollTop.value = scrollTop.value === 0 ? 0.1 : 0
}

// TODO 密码错误标记，可直接更新账号密码
// TODO 密码框代码公共部分提取
// TODO 做一键重试通报
// TODO 做本页直接有重试通报按钮

// 后退刷新
onShow(() => {
  // <scroll-view> 到最顶
  scrollToTop()
  pageIndex.value = 1
  hasMore.value = true
  getReportGoodsList()
  getOverview()
  isShowIncorrectPasswordTip.value = useUserStore().isShowIncorrectPasswordTip()
})
</script>

<template>
  <view>
    <view class="f-search-bar flex items-center bg-white px-3 shadow-blue">
      <view class="o-bg-no flex flex-1 items-center gap-2 py-1 pl-4 pr-3" style="border-radius: 2rem">
        <up-icon :color="Color.gray" name="search" size="20" />
        <up-input
          v-model="keyword" :maxlength="14" border="none" class="o-bg-transparent grow" clearable
          placeholder="条码/商品名称" @change="handleInput"
        />
        <view
          :class="isFilter ? 'text-primary' : 'o-color-aid'" class="flex items-center gap-1 text-sm"
          @click="isShowFilter = !isShowFilter"
        >
          <view class="shrink-0">
            筛选/排序
          </view>
          <view style="min-width: 1.5rem">
            <up-icon v-if="isShowFilter" name="arrow-up" size="14" />
            <up-icon v-else name="arrow-down" size="14" />
          </view>
        </view>
      </view>
    </view>
    <view class="relative">
      <view v-if="isShowFilter" class="absolute left-0 z-10 box-border w-full bg-white p-4 shadow-lg">
        <checkbox-group class="flex flex-wrap gap-2" @change="checkboxChange">
          <label class="mr-2 flex items-center">
            <checkbox
              :color="Color.primary" style="transform: scale(0.7)" :value="`${SyncAddState.success}`"
              :checked="syncAddStateArr.includes(SyncAddState.success)"
            />
            <text class="text-emerald-500">已通报</text>
          </label>
          <label class="flex items-center">
            <checkbox
              :color="Color.primary" style="transform: scale(0.7)" :value="`${SyncAddState.processing}`"
              :checked="syncAddStateArr.includes(SyncAddState.processing)"
            />
            <text class="text-primary">通报中</text>
          </label>
          <label class="mr-2 flex items-center">
            <checkbox
              :color="Color.primary" style="transform: scale(0.7)" :value="`${SyncAddState.failed}`"
              :checked="syncAddStateArr.includes(SyncAddState.failed)"
            />
            <text class="text-red-500">通报失败</text>
          </label>
          <label class="flex items-center">
            <checkbox
              :color="Color.primary" style="transform: scale(0.7)" :value="`${SyncAddState.new}`"
              :checked="syncAddStateArr.includes(SyncAddState.new)"
            />
            <text>未通报</text>
          </label>
        </checkbox-group>
      </view>
    </view>
    <scroll-view
      :scroll-y="true" :refresher-enabled="true" :refresher-triggered="refreshTriggered"
      :scroll-with-animation="true" :scroll-top="scrollTop" class="o-bg-no f-scroll-view" @refresherrefresh="onRefresh"
      @scrolltolower="onScrollToLower"
    >
      <view class="p-3 space-y-2">
        <view
          v-for="item in productList" :key="item.goodsId" class="rounded bg-white p-3"
          @click="showDetail(item.goodsId)"
        >
          <view class="flex items-baseline justify-between text-sm leading-4">
            <view class="text-gray">
              {{ item.barCode }}
            </view>
            <view :class="getStatusStrAndColor(item)[1]">
              {{ getStatusStrAndColor(item)[0] }}
            </view>
          </view>
          <view class="mt-1 flex items-baseline justify-between leading-4 space-x-4">
            <view class="line-clamp-2 flex-1 font-bold">
              {{ item.goodsName }}
            </view>
            <view class="line-clamp-2 shrink-0 text-sm text-gray" style="max-width: 30vw">
              {{ item.brandName }}
            </view>
          </view>
          <view class="mt-1 flex items-baseline justify-between text-sm text-gray leading-4">
            <view class="line-clamp-2" style="max-width: 40vw">
              净含量：{{ item.netContent }}
            </view>
            <view class="line-clamp-2" style="max-width: 30vw">
              规格：{{ item.spec }}
            </view>
          </view>
          <view
            v-if="item.syncAddState === SyncAddState.failed || item.syncAddState === SyncAddState.processing"
            class="o-line my-2"
          />
          <view v-if="item.syncAddMsg && item.syncAddMsg !== ''" class="text-sm text-red-500">
            {{ item.syncAddMsg }}
          </view>
          <view v-if="item.syncMsg && item.syncMsg !== ''" class="text-sm text-red-500">
            {{ item.syncMsg }}
          </view>
          <!-- <view class="mt-2 flex justify-end space-x-4">
            <view
              v-if="isShowIncorrectPasswordTip && item.isAccountError"
              class="rounded bg-gray-100 px-4 py-2 text-red-500"
            >
              更正账号密码
            </view>
            <template v-else>
              <view v-if="item.syncAddState === SyncAddState.failed" class="rounded bg-red-50 px-4 py-2 text-red-500">
                重试通报
              </view>
            </template>
          </view> -->
        </view>
      </view>
      <view v-if="loading" class="flex justify-center py-4">
        <up-loading-icon mode="semicircle" />
        <text class="ml-2 text-gray-500">加载中...</text>
      </view>
      <view v-else-if="!hasMore" class="flex justify-center py-8">
        <text class="text-gray-400">没有更多数据了</text>
      </view>
      <view class="o-pb" />
    </scroll-view>
    <view
      class="pointer-events-none fixed right-0 box-border w-full p-3"
      style="bottom:calc(env(safe-area-inset-bottom) + 10rpx)"
    >
      <view
        v-if="!isShowInfoBar"
        class="o-tabbar-shadow pointer-events-auto float-right w-fit flex items-center rounded-full bg-white py-1 pl-1 pr-5 space-x-1"
        @click="isShowInfoBar = true"
      >
        <view v-if="overviewLoading" class="flex items-center justify-center px-6 py-3">
          <up-loading-icon mode="semicircle" />
        </view>
        <template v-else>
          <template
            v-if="overviewData?.totalGoodsNum === 0"
          >
            <up-icon
              name="plus-circle-fill" size="50" :color="Color.primary"
            />
            <view>
              <view class="text-sm">
                请添加产品
              </view>
              <view class="text-2xs text-gray">
                点击查看概况
              </view>
            </view>
          </template>
          <template
            v-if="overviewData?.successNum > 0 && overviewData?.reportingNum === 0 && overviewData?.failNum === 0"
          >
            <up-icon
              name="checkmark-circle-fill" size="50" :color="Color.emerald"
            />
            <view>
              <view class="text-sm">
                已完成所有通报
              </view>
              <view class="text-2xs text-gray">
                点击查看概况
              </view>
            </view>
          </template>
          <template
            v-if="overviewData?.reportingNum > 0 && overviewData?.failNum === 0"
          >
            <up-icon
              name="clock-fill" size="50" :color="Color.primary"
            />
            <view>
              <view class="text-sm">
                通报中请等待
              </view>
              <view class="text-2xs text-gray">
                点击查看概况
              </view>
            </view>
          </template>
          <template
            v-if="overviewData?.failNum > 0"
          >
            <up-icon
              name="error-circle-fill" size="50" :color="Color.red"
            />
            <view>
              <view class="text-sm">
                有通报失败
              </view>
              <view class="text-2xs text-gray">
                点击查看概况
              </view>
            </view>
          </template>
        </template>
      </view>
      <view
        v-if="isShowInfoBar"
        class="o-tabbar-shadow f-Info-box pointer-events-auto box-border w-full rounded-lg bg-white p-4"
      >
        <view class="mb-2 text-xl font-bold">
          产品通报概况
        </view>
        <up-skeleton rows="2" :loading="overviewLoading">
          <view>
            <!-- <view class="flex items-center justify-between text-gray">
              <view class="shrink-0">
                条码容量占用情况：
              </view>
              <view class="flex">
                <view>已使用：21</view>
                <view>
                  <text class="px-1">
                    /
                  </text>总量：10000
                </view>
              </view>
            </view>
            <up-line-progress class="mt-2" :percentage="30" :active-color="Color.primary" /> -->

            <view class="grid grid-cols-4 text-sm">
              <view class="flex flex-col items-center text-emerald-500">
                <view>已通报</view>
                <view class="text-lg font-bold">
                  {{ overviewData?.successNum }}
                </view>
              </view>
              <view
                class="flex flex-col items-center border-l border-r border-l-gray-200 border-r-gray-200 border-l-solid border-r-solid text-primary"
              >
                <view>通报中</view>
                <view class="text-lg font-bold">
                  {{ overviewData?.reportingNum }}
                </view>
              </view>
              <view
                class="flex flex-col items-center border-r border-r-gray-200 border-r-solid"
                :class="overviewData?.failNum > 0 ? 'text-red-500' : 'text-gray-500'"
              >
                <view>通报失败</view>
                <view class="text-lg font-bold">
                  {{ overviewData?.failNum }}
                </view>
              </view>
              <view class="flex flex-col items-center">
                <view>产品总数</view>
                <view class="text-lg font-bold">
                  {{ overviewData?.totalGoodsNum }}
                </view>
              </view>
            </view>

            <!-- <view class="mt-4 flex items-center justify-between space-x-2">
              <view class="center flex-1 rounded bg-gray-100 py-2 text-amber-500">
                一键取消正在通报
              </view>
              <view class="center flex-1 rounded bg-red-50 py-2 text-red-500">
                一键重试失败通报
              </view>
            </view> -->
          </view>
        </up-skeleton>
        <view class="mt-4 flex items-end text-sm space-x-2">
          <view
            class="f-big-btn o-btn-light-bg-blue-border o-shadow-blue-light center flex-1 flex-col"
            @click="toCreateQuickReport"
          >
            <image :src="DesignImage.btnIcon.cameras" mode="widthFix" class="f-btn-icon-md" />
            <text class="f-btn-text">
              拍照自主通报
            </text>
          </view>
          <view
            class="f-big-btn o-btn-light-bg-blue-border o-shadow-blue-light center flex-1 flex-col"
            @click="toInfoReport"
          >
            <image :src="DesignImage.btnIcon.file" mode="widthFix" class="f-btn-icon-md" />
            <text class="f-btn-text">
              批量代办通报
            </text>
          </view>
          <view
            class="f-c-btn center shrink-0 rounded-full bg-primary text-xs text-white"
            @click="isShowInfoBar = false"
          >
            收起
          </view>
        </view>
      </view>
    </view>
    <up-modal
      :show="isShowKeyModal" :show-cancel-button="true" title="条码成员身份校验" width="650rpx" confirm-text="提交"
      @confirm="savePasswordAndReport" @cancel="isShowKeyModal = false"
    >
      <view>
        <view class="f-form-item">
          <view class="f-label">
            <text class="o-form-require pr-1">
              条码卡号
            </text>
          </view>
          <view class="o-form-underline flex-1">
            <input v-model.trim="barCodeCardNum">
          </view>
        </view>
        <view class="f-form-item">
          <view class="f-label">
            <text class="o-form-require pr-1">
              条码卡号
            </text>
          </view>
          <view class="o-form-underline flex-1">
            <input v-model.trim="barCodeCardPassword">
          </view>
        </view>
      </view>
    </up-modal>
  </view>
</template>

<style lang="scss" scoped>
$h: 96rpx;

.f-search-bar {
  height: $h;
}

.f-scroll-view {
  height: calc(100vh - $h - 20rpx);
}

.f-Info-box {
  border-radius: 6px 6px 36px 6px;
}

.f-big-btn {
  height: 146rpx;
}

.f-btn-icon-md {
  width: 106rpx;
}

.f-c-btn {
  $w: 74rpx;
  height: $w;
  width: $w;
}
</style>
