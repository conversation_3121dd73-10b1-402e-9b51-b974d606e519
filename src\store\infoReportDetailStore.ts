import type { ReportGoodsBaseParams, ReportGoodsLoadV2Res } from '@/service/infoReportApi'
import { defineStore } from 'pinia'
import { CurrencyType } from '@/enums'

export const infoReportDetailStore = defineStore('infoReportDetailStore', () => {
  const infoReportDetailData = ref<ReportGoodsBaseParams>()
  const gpcTypeName = ref('')

  const setReportData = (data: ReportGoodsLoadV2Res['data']) => {
    console.log('🚀 ~ setReportData ~ data:', data)
    // 处理 currency 类型转换
    let currency: CurrencyType | undefined
    if (data.currency) {
      // 检查 currency 是否是有效的 CurrencyType 值
      const validCurrencies = Object.values(CurrencyType) as string[]
      if (validCurrencies.includes(data.currency)) {
        currency = data.currency as CurrencyType
      }
      else {
        // 如果不是有效值，使用默认值
        currency = CurrencyType.RMB
      }
    }

    // 转换数据类型
    const reportData: ReportGoodsBaseParams = {
      ...data,
      currency,
    }

    infoReportDetailData.value = reportData
    gpcTypeName.value = data.gpcTypeName
  }

  const clearReportData = () => {
    infoReportDetailData.value = undefined
    gpcTypeName.value = ''
  }

  return {
    infoReportDetailData,
    gpcTypeName,
    setReportData,
    clearReportData,
  }
})
