{"name": "barcode_mini_app", "type": "module", "version": "0.0.1", "description": "", "engines": {"node": ">=18", "pnpm": ">=7.30"}, "scripts": {"dev:mp": "uni -p mp-weixin", "build:mp": "uni build -p mp-weixin", "selectPlatform:wx": "node scripts/selectPlatform.cjs wx", "selectPlatform:wx_tiaoMaBang": "node scripts/selectPlatform.cjs wx_tiaoMaBang", "selectPlatform:douYin": "node scripts/selectPlatform.cjs douYin", "type-check": "vue-tsc --noEmit", "lint": "eslint", "lint:fix": "eslint --fix"}, "dependencies": {"@alova/adapter-uniapp": "^2.0.14", "@alova/shared": "^1.3.1", "@dcloudio/uni-app": "3.0.0-4070520250711001", "@dcloudio/uni-app-harmony": "3.0.0-4070520250711001", "@dcloudio/uni-app-plus": "3.0.0-4070520250711001", "@dcloudio/uni-components": "3.0.0-4070520250711001", "@dcloudio/uni-h5": "3.0.0-4070520250711001", "@dcloudio/uni-mp-alipay": "3.0.0-4070520250711001", "@dcloudio/uni-mp-baidu": "3.0.0-4070520250711001", "@dcloudio/uni-mp-harmony": "3.0.0-4070520250711001", "@dcloudio/uni-mp-jd": "3.0.0-4070520250711001", "@dcloudio/uni-mp-kuaishou": "3.0.0-4070520250711001", "@dcloudio/uni-mp-lark": "3.0.0-4070520250711001", "@dcloudio/uni-mp-qq": "3.0.0-4070520250711001", "@dcloudio/uni-mp-toutiao": "3.0.0-4070520250711001", "@dcloudio/uni-mp-weixin": "3.0.0-4070520250711001", "@dcloudio/uni-mp-xhs": "3.0.0-4070520250711001", "@dcloudio/uni-quickapp-webview": "3.0.0-4070520250711001", "@tanstack/vue-query": "^5.62.16", "abortcontroller-polyfill": "^1.7.8", "alova": "^3.3.3", "clipboard": "^2.0.11", "crypto-js": "^4.2.0", "dayjs": "1.11.10", "debounce": "^2.2.0", "pinia": "2.0.36", "pinia-plugin-persistedstate": "3.2.1", "uview-plus": "^3.4.69", "vue": "3.4.21", "z-paging": "2.8.7"}, "devDependencies": {"@antfu/eslint-config": "^4.15.0", "@babel/plugin-proposal-private-property-in-object": "^7.21.11", "@commitlint/cli": "^19.8.1", "@commitlint/config-conventional": "^19.8.1", "@dcloudio/types": "^3.4.16", "@dcloudio/uni-automator": "3.0.0-4070520250711001", "@dcloudio/uni-cli-shared": "3.0.0-4070520250711001", "@dcloudio/uni-stacktracey": "3.0.0-4070520250711001", "@dcloudio/vite-plugin-uni": "3.0.0-4070520250711001", "@douyin-microapp/typings": "^1.3.1", "@esbuild/darwin-arm64": "0.20.2", "@esbuild/darwin-x64": "0.20.2", "@iconify-json/carbon": "^1.2.4", "@rollup/rollup-darwin-x64": "^4.28.0", "@types/crypto-js": "^4.2.2", "@types/node": "^20.17.9", "@types/wechat-miniprogram": "^3.4.8", "@uni-helper/eslint-config": "^0.4.0", "@uni-helper/plugin-uni": "0.1.0", "@uni-helper/uni-types": "^1.0.0-alpha.6", "@uni-helper/unocss-preset-uni": "^0.2.11", "@uni-helper/vite-plugin-uni-components": "0.2.0", "@uni-helper/vite-plugin-uni-layouts": "0.1.11", "@uni-helper/vite-plugin-uni-manifest": "0.2.8", "@uni-helper/vite-plugin-uni-pages": "0.2.29", "@uni-helper/vite-plugin-uni-platform": "0.0.5", "@uni-ku/bundle-optimizer": "^1.3.3", "@unocss/eslint-plugin": "^66.2.3", "@unocss/preset-legacy-compat": "^0.59.4", "@vue/runtime-core": "3.4.21", "@vue/tsconfig": "^0.1.3", "autoprefixer": "^10.4.20", "eslint": "^9.31.0", "eslint-plugin-format": "^1.0.1", "husky": "^9.1.7", "lint-staged": "^15.2.10", "miniprogram-api-typings": "^4.1.0", "openapi-ts-request": "^1.1.2", "postcss": "^8.4.49", "postcss-html": "^1.7.0", "postcss-scss": "^4.0.9", "rollup-plugin-visualizer": "^5.12.0", "sass": "1.77.8", "terser": "^5.36.0", "typescript": "^5.7.2", "unocss": "66.0.0", "unplugin-auto-import": "^0.17.8", "vite": "5.2.8", "vite-plugin-restart": "^0.4.2", "vue-tsc": "^2.2.10"}, "resolutions": {"bin-wrapper": "npm:bin-wrapper-china"}, "lint-staged": {"*": "eslint --fix"}, "update-time": "2025-08-25"}